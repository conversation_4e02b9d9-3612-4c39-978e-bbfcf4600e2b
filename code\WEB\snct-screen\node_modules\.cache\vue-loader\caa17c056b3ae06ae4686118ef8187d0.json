{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\user\\login.vue?vue&type=template&id=b149b182&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\user\\login.vue", "mtime": 1754370729979}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753075298398}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}