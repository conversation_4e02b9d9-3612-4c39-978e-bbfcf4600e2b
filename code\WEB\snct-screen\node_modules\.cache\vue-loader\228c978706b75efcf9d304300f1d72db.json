{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\user\\login.vue?vue&type=style&index=0&id=b149b182&rel=stylesheet%2Fscss&lang=scss&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\user\\login.vue", "mtime": 1754370729979}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoubG9naW4gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgaGVpZ2h0OiAxMDAlOw0KICBiYWNrZ3JvdW5kOiB1cmwoJ35AL2Fzc2V0cy9pbWcvc2hpcC5wbmcnKSBuby1yZXBlYXQgY2VudGVyIGNlbnRlcjsNCiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjsNCn0NCg0KLnRpdGxlIHsNCiAgbWFyZ2luOiAwcHggYXV0byAzMHB4IGF1dG87DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM3MDcwNzA7DQp9DQoNCi5sb2dpbi1mb3JtIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICB3aWR0aDogMzUwcHg7DQogIHBhZGRpbmc6IDI1cHggMjVweCA1cHggMjVweDsNCg0KICAuZWwtaW5wdXQgew0KICAgIGhlaWdodDogMzhweDsNCiAgICBpbnB1dCB7DQogICAgICBoZWlnaHQ6IDM4cHg7DQogICAgfQ0KICB9DQoNCiAgLmlucHV0LWljb24gew0KICAgIGhlaWdodDogMzlweDsNCiAgICB3aWR0aDogMTRweDsNCiAgICBtYXJnaW4tbGVmdDogMnB4Ow0KICB9DQoNCiAgLnNob3ctcHdkIHsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgcmlnaHQ6IDEycHg7DQogICAgdG9wOiA1MCU7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBjb2xvcjogI2MwYzRjYzsNCiAgICBmb250LXNpemU6IDE2cHg7DQoNCiAgICAmOmhvdmVyIHsNCiAgICAgIGNvbG9yOiAjNDA5ZWZmOw0KICAgIH0NCiAgfQ0KfQ0KDQoubG9naW4tdGlwIHsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjYmZiZmJmOw0KfQ0KDQouZWwtbG9naW4tZm9vdGVyIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBsaW5lLWhlaWdodDogNDBweDsNCiAgcG9zaXRpb246IGZpeGVkOw0KICBib3R0b206IDA7DQogIHdpZHRoOiAxMDAlOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LWZhbWlseTogQXJpYWw7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbGV0dGVyLXNwYWNpbmc6IDFweDsNCn0NCg=="}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AA2NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views/user", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\r\n      <h3 class=\"title\">SNCT智慧船情大屏系统</h3>\r\n\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          ref=\"username\"\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n          name=\"username\"\r\n          tabindex=\"1\"\r\n        >\r\n          <i slot=\"prefix\" class=\"el-icon-user el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          ref=\"password\"\r\n          v-model=\"loginForm.password\"\r\n          :type=\"passwordType\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          name=\"password\"\r\n          tabindex=\"2\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <i slot=\"prefix\" class=\"el-icon-lock el-input__icon input-icon\" />\r\n          <i\r\n            slot=\"suffix\"\r\n            :class=\"passwordType === 'password' ? 'el-icon-view' : 'el-icon-hide'\"\r\n            class=\"show-pwd\"\r\n            @click=\"showPwd\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 滑块验证组件 -->\r\n    <Verify\r\n      ref=\"verify\"\r\n      :captcha-type=\"captchaType\"\r\n      :mode=\"'pop'\"\r\n      @success=\"onVerifySuccess\"\r\n      @error=\"onVerifyError\"\r\n    />\r\n\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2025 SNCT All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { dataModule } from \"../../utils/webSocket.js\";\r\nimport { login } from '@/api/login'\r\nimport { setToken,setUtype } from '@/utils/auth'\r\nimport Verify from '@/components/Verifition/Verify.vue'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: {\r\n    Verify\r\n  },\r\n  data() {\r\n    return {\r\n      loginForm: {\r\n        username: '',\r\n        password: '',\r\n        type: 1,\r\n        code: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\r\n        ]\r\n      },\r\n      loading: false,\r\n      passwordType: 'password',\r\n      captchaType: 'clickWord', // 滑块验证类型\r\n      verifyToken: '', // 验证码token\r\n      redirect: undefined\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        this.redirect = route.query && route.query.redirect;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n    console.log('登录页面########:', dataModule.D0A01)\r\n    \r\n    if (this.loginForm.username === '') {\r\n      this.$refs.username.focus()\r\n    } else if (this.loginForm.password === '') {\r\n      this.$refs.password.focus()\r\n    }\r\n  },\r\n  methods: {\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          // 显示滑块验证\r\n          this.$refs.verify.clickShow = true\r\n        } else {\r\n          console.log('表单验证失败')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n\r\n    // 滑块验证成功回调\r\n    onVerifySuccess(params) {\r\n      console.log('验证成功:', params)\r\n      this.verifyToken = params.captchaVerification\r\n      this.doLogin()\r\n    },\r\n\r\n    // 滑块验证失败回调\r\n    onVerifyError() {\r\n      console.log('验证失败')\r\n      this.$message.error('验证失败，请重试')\r\n    },\r\n\r\n    // 执行登录请求\r\n    doLogin() {\r\n      this.loading = true\r\n      const loginData = {\r\n        username: this.loginForm.username,\r\n        password: this.loginForm.password,\r\n        type: this.loginForm.type,\r\n        code: this.verifyToken\r\n      }\r\n\r\n      login(loginData)\r\n        .then(response => {\r\n          console.log('登录响应:', response)\r\n          if (response.code === 200) {\r\n            const token = response.token || (response.data && response.data.token)\r\n            const utype = response.type || (response.type && response.data.type)\r\n            if (token) {\r\n              setToken(token)\r\n              setUtype(utype)\r\n              console.log('登录成功');\r\n              //this.$message.success('登录成功')\r\n              if('U01'==utype){\r\n                this.$router.push({ path: this.redirect || '/index' }).catch(()=>{})\r\n              }else{\r\n                this.$router.push({ path: this.redirect || '/comindex' }).catch(()=>{})\r\n              }\r\n            } else {\r\n              this.$message.error('登录失败：未获取到token')\r\n            }\r\n          } else if (response.repCode === '0000') {\r\n            const token = response.repData && response.repData.token\r\n            const utype = response.repData && response.repData.utype\r\n            if (token) {\r\n              setToken(token)\r\n              setUtype(utype)\r\n              console.log('登录成功');\r\n              //this.$message.success('登录成功')\r\n              if('U01'==utype){\r\n                this.$router.push({ path: this.redirect || '/index' }).catch(()=>{})\r\n              }else{\r\n                this.$router.push({ path: this.redirect || '/comindex' }).catch(()=>{})\r\n              }\r\n            } else {\r\n              this.$message.error('登录失败：未获取到token')\r\n            }\r\n          } else {\r\n            const errorMsg = response.msg || response.repMsg || '登录失败'\r\n            this.$message.error(errorMsg)\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false\r\n          this.verifyToken = ''\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background: url('~@/assets/img/ship.png') no-repeat center center;\r\n  background-size: cover;\r\n}\r\n\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 350px;\r\n  padding: 25px 25px 5px 25px;\r\n\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n\r\n  .show-pwd {\r\n    position: absolute;\r\n    right: 12px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    cursor: pointer;\r\n    color: #c0c4cc;\r\n    font-size: 16px;\r\n\r\n    &:hover {\r\n      color: #409eff;\r\n    }\r\n  }\r\n}\r\n\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n</style>"]}]}