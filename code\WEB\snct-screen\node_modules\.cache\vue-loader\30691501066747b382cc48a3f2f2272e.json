{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\user\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\user\\login.vue", "mtime": 1754370729979}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AAuEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views/user", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\r\n      <h3 class=\"title\">SNCT智慧船情大屏系统</h3>\r\n\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          ref=\"username\"\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n          name=\"username\"\r\n          tabindex=\"1\"\r\n        >\r\n          <i slot=\"prefix\" class=\"el-icon-user el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          ref=\"password\"\r\n          v-model=\"loginForm.password\"\r\n          :type=\"passwordType\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          name=\"password\"\r\n          tabindex=\"2\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <i slot=\"prefix\" class=\"el-icon-lock el-input__icon input-icon\" />\r\n          <i\r\n            slot=\"suffix\"\r\n            :class=\"passwordType === 'password' ? 'el-icon-view' : 'el-icon-hide'\"\r\n            class=\"show-pwd\"\r\n            @click=\"showPwd\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 滑块验证组件 -->\r\n    <Verify\r\n      ref=\"verify\"\r\n      :captcha-type=\"captchaType\"\r\n      :mode=\"'pop'\"\r\n      @success=\"onVerifySuccess\"\r\n      @error=\"onVerifyError\"\r\n    />\r\n\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2025 SNCT All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { dataModule } from \"../../utils/webSocket.js\";\r\nimport { login } from '@/api/login'\r\nimport { setToken,setUtype } from '@/utils/auth'\r\nimport Verify from '@/components/Verifition/Verify.vue'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: {\r\n    Verify\r\n  },\r\n  data() {\r\n    return {\r\n      loginForm: {\r\n        username: '',\r\n        password: '',\r\n        type: 1,\r\n        code: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\r\n        ]\r\n      },\r\n      loading: false,\r\n      passwordType: 'password',\r\n      captchaType: 'clickWord', // 滑块验证类型\r\n      verifyToken: '', // 验证码token\r\n      redirect: undefined\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        this.redirect = route.query && route.query.redirect;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n    console.log('登录页面########:', dataModule.D0A01)\r\n    \r\n    if (this.loginForm.username === '') {\r\n      this.$refs.username.focus()\r\n    } else if (this.loginForm.password === '') {\r\n      this.$refs.password.focus()\r\n    }\r\n  },\r\n  methods: {\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          // 显示滑块验证\r\n          this.$refs.verify.clickShow = true\r\n        } else {\r\n          console.log('表单验证失败')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n\r\n    // 滑块验证成功回调\r\n    onVerifySuccess(params) {\r\n      console.log('验证成功:', params)\r\n      this.verifyToken = params.captchaVerification\r\n      this.doLogin()\r\n    },\r\n\r\n    // 滑块验证失败回调\r\n    onVerifyError() {\r\n      console.log('验证失败')\r\n      this.$message.error('验证失败，请重试')\r\n    },\r\n\r\n    // 执行登录请求\r\n    doLogin() {\r\n      this.loading = true\r\n      const loginData = {\r\n        username: this.loginForm.username,\r\n        password: this.loginForm.password,\r\n        type: this.loginForm.type,\r\n        code: this.verifyToken\r\n      }\r\n\r\n      login(loginData)\r\n        .then(response => {\r\n          console.log('登录响应:', response)\r\n          if (response.code === 200) {\r\n            const token = response.token || (response.data && response.data.token)\r\n            const utype = response.type || (response.type && response.data.type)\r\n            if (token) {\r\n              setToken(token)\r\n              setUtype(utype)\r\n              console.log('登录成功');\r\n              //this.$message.success('登录成功')\r\n              if('U01'==utype){\r\n                this.$router.push({ path: this.redirect || '/index' }).catch(()=>{})\r\n              }else{\r\n                this.$router.push({ path: this.redirect || '/comindex' }).catch(()=>{})\r\n              }\r\n            } else {\r\n              this.$message.error('登录失败：未获取到token')\r\n            }\r\n          } else if (response.repCode === '0000') {\r\n            const token = response.repData && response.repData.token\r\n            const utype = response.repData && response.repData.utype\r\n            if (token) {\r\n              setToken(token)\r\n              setUtype(utype)\r\n              console.log('登录成功');\r\n              //this.$message.success('登录成功')\r\n              if('U01'==utype){\r\n                this.$router.push({ path: this.redirect || '/index' }).catch(()=>{})\r\n              }else{\r\n                this.$router.push({ path: this.redirect || '/comindex' }).catch(()=>{})\r\n              }\r\n            } else {\r\n              this.$message.error('登录失败：未获取到token')\r\n            }\r\n          } else {\r\n            const errorMsg = response.msg || response.repMsg || '登录失败'\r\n            this.$message.error(errorMsg)\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false\r\n          this.verifyToken = ''\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background: url('~@/assets/img/ship.png') no-repeat center center;\r\n  background-size: cover;\r\n}\r\n\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 350px;\r\n  padding: 25px 25px 5px 25px;\r\n\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n\r\n  .show-pwd {\r\n    position: absolute;\r\n    right: 12px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    cursor: pointer;\r\n    color: #c0c4cc;\r\n    font-size: 16px;\r\n\r\n    &:hover {\r\n      color: #409eff;\r\n    }\r\n  }\r\n}\r\n\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n</style>"]}]}