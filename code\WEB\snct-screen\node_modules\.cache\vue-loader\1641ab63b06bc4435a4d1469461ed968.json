{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\BigMap.vue?vue&type=style&index=0&id=5b5fb6ff&scoped=true&lang=css&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\BigMap.vue", "mtime": 1754278251789}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5iaWctbWFwLWNvbnRhaW5lciB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKfQoKLyogLm1hcC13cmFwcGVyIHsKICBib3JkZXI6IDJweCBzb2xpZCAjMGVjNmM0OwogIGJvcmRlci1yYWRpdXM6IDRweDsKfSAqLwoKLm1hcC1jb250cm9scyB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogMTBweDsKICByaWdodDogMTBweDsKICB6LWluZGV4OiAxMDAwOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDVweDsKfQoKLmNvbnRyb2wtYnRuIHsKICB3aWR0aDogMzJweDsKICBoZWlnaHQ6IDMycHg7CiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpOwogIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7Cn0KCi5jb250cm9sLWJ0bjpob3ZlciB7CiAgYmFja2dyb3VuZDogI2ZmZjsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKfQoKLmxheWVyLXBhbmVsIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiAxMHB4OwogIHJpZ2h0OiA1MHB4OwogIHotaW5kZXg6IDEwMDA7CiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTsKICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBwYWRkaW5nOiA4cHg7CiAgbWluLXdpZHRoOiAxMDBweDsKfQoKLmxheWVyLWl0ZW0gewogIHBhZGRpbmc6IDZweCAxMnB4OwogIGN1cnNvcjogcG9pbnRlcjsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7Cn0KCi5sYXllci1pdGVtOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjZjVmNWY1Owp9CgoubGF5ZXItaXRlbS5hY3RpdmUgewogIGJhY2tncm91bmQ6ICM0MDllZmY7CiAgY29sb3I6IHdoaXRlOwp9Cg=="}, {"version": 3, "sources": ["BigMap.vue"], "names": [], "mappings": ";AAmQA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "BigMap.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\n  <div class=\"big-map-container\">\n    <div \n      :id=\"mapId\" \n      class=\"map-wrapper\"\n      :style=\"{ width: width, height: height }\"\n    ></div>\n    \n    <!-- 地图控制按钮 -->\n    <div class=\"map-controls\" v-if=\"showControls\">\n      <div class=\"control-btn\" @click=\"toggleLayer\" title=\"切换图层\">\n        <i class=\"el-icon-picture\"></i>\n      </div>\n      <div class=\"control-btn\" @click=\"resetView\" title=\"重置视图\">\n        <i class=\"el-icon-refresh\"></i>\n      </div>\n      <div class=\"control-btn\" @click=\"toggleFullscreen\" title=\"全屏\">\n        <i class=\"el-icon-full-screen\"></i>\n      </div>\n    </div>\n\n    <!-- 图层切换面板 -->\n    <div class=\"layer-panel\" v-show=\"showLayerPanel\">\n      <div class=\"layer-item\" \n           v-for=\"layer in availableLayers\" \n           :key=\"layer.key\"\n           :class=\"{ active: currentLayer === layer.key }\"\n           @click=\"switchLayer(layer.key)\">\n        {{ layer.name }}\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapConfig } from '@/utils/map/mapConfig'\nimport { mapUtils } from '@/utils/map/mapUtils'\n\nexport default {\n  name: 'BigMap',\n  props: {\n    // 地图容器ID\n    mapId: {\n      type: String,\n      default: 'bigemap-container'\n    },\n    // 地图宽度\n    width: {\n      type: String,\n      default: '100%'\n    },\n    // 地图高度\n    height: {\n      type: String,\n      default: '100%'\n    },\n    // 初始中心点\n    center: {\n      type: Array,\n      default: () => [120.0, 30.0]\n    },\n    // 初始缩放级别\n    zoom: {\n      type: Number,\n      default: 7\n    },\n    // 是否显示控制按钮\n    showControls: {\n      type: Boolean,\n      default: true\n    },\n    // 地图配置选项\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      map: null,\n      currentLayer: 'satellite',\n      showLayerPanel: false,\n      availableLayers: [\n        { key: 'satellite', name: '卫星图' },\n        { key: 'sea', name: '海图' },\n        { key: 'street', name: '街道图' }\n      ],\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.initMap()\n  },\n  beforeDestroy() {\n    if (this.map) {\n      this.map.remove()\n    }\n  },\n  methods: {\n    // 初始化地图\n    async initMap() {\n      try {\n        // 等待 bigemap 库加载完成\n        // await this.waitForBigemap()\n\n        // 设置地图服务器地址和访问令牌\n        BM.Config.HTTP_URL = mapConfig.mapHost\n        BM.accessToken = mapConfig.accessToken || 'pk.eyJ1IjoiY3VzXzg3a3J5a3Y4IiwiYSI6IjdmZzdsdWI0eDVtYmF6ZWRoOWxudmt2ZDEifQ.hbJM3hwBW1MVSGg3eiWdNQ'\n\n        // 创建地图实例\n        this.map = BM.map(this.mapId, 'bigemap.tian-map', {\n          center: [this.center[0], this.center[1]],\n          zoom: this.zoom,\n          zoomControl: true,\n          minZoom: 3,\n          maxZoom: 18,\n          attributionControl: false,\n          ...this.options\n        })\n\n        // 添加默认图层\n        this.addDefaultLayers()\n\n        // 绑定地图事件\n        this.bindMapEvents()\n\n        // 设置地图边界以确保显示内容\n        this.map.fitBounds([[15.792253494262695, 109.2919921875], [33.90689468383789, 126.826171875]])\n\n        // 触发地图初始化完成事件\n        this.$emit('map-ready', this.map)\n\n      } catch (error) {\n        console.error('地图初始化失败:', error)\n        this.$emit('map-error', error)\n      }\n    },\n\n    // 等待 bigemap 库加载\n    waitForBigemap() {\n      return new Promise((resolve, reject) => {\n        if (window.BM) {\n          resolve()\n          return\n        }\n\n        let attempts = 0\n        const maxAttempts = 50\n        const checkInterval = setInterval(() => {\n          attempts++\n          if (window.BM) {\n            clearInterval(checkInterval)\n            resolve()\n          } else if (attempts >= maxAttempts) {\n            clearInterval(checkInterval)\n            reject(new Error('Bigemap library failed to load'))\n          }\n        }, 100)\n      })\n    },\n\n    // 添加默认图层\n    addDefaultLayers() {\n      // 创建卫星图层\n      this.satelliteLayer = BM.tileLayer('bigemap.tian-satellite')\n\n      // 创建海图图层\n      this.seaLayer = BM.tileLayer('bigemap.tian-terrain')\n\n      // 默认添加卫星图层\n      this.satelliteLayer.addTo(this.map)\n    },\n\n    // 绑定地图事件\n    bindMapEvents() {\n      this.map.on('click', (e) => {\n        this.$emit('map-click', e)\n      })\n      \n      this.map.on('zoom', (e) => {\n        this.$emit('map-zoom', e)\n      })\n      \n      this.map.on('moveend', (e) => {\n        this.$emit('map-moveend', e)\n      })\n    },\n\n    // 切换图层面板显示\n    toggleLayer() {\n      this.showLayerPanel = !this.showLayerPanel\n    },\n\n    // 切换地图图层\n    switchLayer(layerKey) {\n      if (this.currentLayer === layerKey) return\n\n      // 移除当前图层\n      if (this.currentLayer === 'satellite') {\n        this.map.removeLayer(this.satelliteLayer)\n      } else if (this.currentLayer === 'sea') {\n        this.map.removeLayer(this.seaLayer)\n      }\n\n      // 添加新图层\n      if (layerKey === 'satellite') {\n        this.satelliteLayer.addTo(this.map)\n      } else if (layerKey === 'sea') {\n        this.seaLayer.addTo(this.map)\n      }\n\n      this.currentLayer = layerKey\n      this.showLayerPanel = false\n      this.$emit('layer-changed', layerKey)\n    },\n\n    // 重置视图\n    resetView() {\n      this.map.setView([this.center[0], this.center[1]], this.zoom)\n    },\n\n    // 切换全屏\n    toggleFullscreen() {\n      this.isFullscreen = !this.isFullscreen\n      this.$emit('fullscreen-toggle', this.isFullscreen)\n    },\n\n    // 获取地图实例\n    getMap() {\n      return this.map\n    },\n\n    // 设置地图中心点\n    setCenter(lng, lat, zoom) {\n      if (this.map) {\n        this.map.setView([lng, lat], zoom || this.map.getZoom())\n      }\n    },\n\n    // 添加标记\n    addMarker(lng, lat, options = {}) {\n      if (!this.map) return null\n\n      const marker = BM.marker([lng, lat], options)\n      marker.addTo(this.map)\n      return marker\n    },\n\n    // 移除标记\n    removeMarker(marker) {\n      if (this.map && marker) {\n        this.map.removeLayer(marker)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.big-map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n/* .map-wrapper {\n  border: 2px solid #0ec6c4;\n  border-radius: 4px;\n} */\n\n.map-controls {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.control-btn {\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.control-btn:hover {\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n.layer-panel {\n  position: absolute;\n  top: 10px;\n  right: 50px;\n  z-index: 1000;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 8px;\n  min-width: 100px;\n}\n\n.layer-item {\n  padding: 6px 12px;\n  cursor: pointer;\n  border-radius: 3px;\n  transition: all 0.3s;\n}\n\n.layer-item:hover {\n  background: #f5f5f5;\n}\n\n.layer-item.active {\n  background: #409eff;\n  color: white;\n}\n</style>\n"]}]}